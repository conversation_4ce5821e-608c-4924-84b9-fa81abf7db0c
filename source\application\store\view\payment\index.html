<!--[meiye_07]-->
<div class="layui-fluid">
  <div id="paymentList" v-cloak style="min-height: 100%">
    <el-card class="box-card" v-loading="paymentLoading">
      <div>
        <div class="">
          <el-form
            :inline="true"
            class="demo-form-inline"
            size="small"
            @submit.native.prevent
          >
            <el-form-item label="交易时间：">
              <el-date-picker
                size="small"
                v-model="timeArr"
                type="daterange"
                align="right"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="['00:00:00', '23:59:59']"
                :picker-options="pickerOptions"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="全部类型：">
              <el-select
                v-model="order_type"
                size="small"
                @change="current_change(1)"
                style="width: 160px"
              >
                <el-option label="全部" value="0"></el-option>
                <el-option
                  v-for="item in order_types"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="付款方式：">
              <el-select
                v-model="Payment_method"
                size="small"
                @change="current_change(1)"
                style="width: 160px"
              >
                <el-option label="全部" value="0"></el-option>
                <el-option
                  v-for="item in Payment_methods"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-input
                placeholder="可输入客户名、备注名、手机、会员号"
                v-model="keywork"
                clearable
                @keyup.enter.native="current_change(1)"
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="current_change(1)"
                ></el-button>
              </el-input>
            </el-form-item>
            <el-button
              type="primary"
              @click="importOrder"
              size="small"
              plain
              style="float: right"
            >
              导出订单
            </el-button>
          </el-form>
        </div>
        <div class="mb-4 w-full grid grid-cols-3 space-x-4">
          <el-card shadow="none">
            <div class="text-gr">支付成功</div>
            <div class="text-2xl font-bold">
              <span class="text-sm pr-1">￥</span>{{form.sPay}}
            </div>
          </el-card>
          <el-card shadow="none">
            <div>退款成功</div>
            <div class="text-2xl font-bold">
              <span class="text-sm pr-1">￥</span>{{form.rPay}}
            </div>
          </el-card>
          <el-card shadow="none">
            <div>等待支付</div>
            <div class="text-2xl font-bold">
              <span class="text-sm pr-1">￥</span>{{form.wPay}}
            </div>
          </el-card>
        </div>
        <div>
          <el-table :data="tableData" style="width: 100%">
            <el-table-column
              prop="out_trsade_no"
              label="创建时间/流水号"
              width="180"
            >
              <template slot-scope="scope">
                <div>{{scope.row.cdate}}</div>
                <div class="text-xs">{{scope.row.out_trade_no}}</div>
              </template>
            </el-table-column>
            <el-table-column prop="price" label="金额" align="right">
            </el-table-column>
            <el-table-column prop="payTypeName" label="支付方式">
              <template slot-scope="scope">
                <div
                  class="text-xs px-2 py-0.5 w-fit rounded"
                  :class="getPayTypeNameColor(scope.row.payTypeName)"
                >
                  {{scope.row.payTypeName}}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="id" label="客户信息">
              <template slot-scope="scope">
                <div class="text-xs">{{scope.row.memberInfo?.member_name}}</div>
                <div class="text-xs">{{scope.row.memberInfo?.phone}}</div>
              </template>
            </el-table-column>
            <el-table-column prop="storetag" label="门店/收银员">
              <template slot-scope="scope">
                <div class="text-xs">
                  <span class="text-gray">门店名：</span>{{scope.row.storetag}}
                </div>
                <div class="text-xs">
                  <span class="text-gray">收银员：</span>{{scope.row.Cashier}}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="order_state" label="状态" align="right">
              <template slot-scope="scope">
                <div v-html="scope.row.order_state"></div>
              </template>
            </el-table-column>
            <el-table-column prop="source" label="流水来源">
              <template slot-scope="scope">
                <div
                  class="text-xs px-2 py-0.5 w-fit rounded"
                  :class="getSourceColor(scope.row.source_type)"
                >
                  {{scope.row.source}}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="" label="操作" align="right">
              <template slot-scope="scope">
                <div style="color: #c1c1c1" v-if="scope.row.source_type == 2">
                  暂无关联订单
                </div>
                <el-button
                  @click="See(scope.row.source_id)"
                  type="text"
                  size="mini"
                  v-else
                >
                  关联订单详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="overflow: hidden" v-if="total>0">
            <el-pagination
              style="margin-top: 10px; float: right"
              background
              @size-change="size_change"
              @current-change="current_change"
              :pager-count="5"
              :page-sizes="[10,20,30,40,50,60,70,80,90,100]"
              :page-size="10"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</div>
<script>
  layui.use("index", function () {
    var $ = layui.$;
    new Vue({
      el: "#paymentList",
      data() {
        return {
          user: "",
          loading2: false,
          order_type: "0",
          order_types: [
            {
              value: "1",
              label: "品项",
            },
            {
              value: "2",
              label: "售卡",
            },
            /* {
              value: "3",
              label: "充值",
            }, */
          ],
          Payment_methods: [
            {
              value: "1",
              label: "微信",
            },
            {
              value: "2",
              label: "支付宝",
            },
            {
              value: "3",
              label: "现金",
            },
            {
              value: "4",
              label: "小程序微信",
            },
            /* {
              value: "5",
              label: "会员余额",
            }, */
            {
              value: "10",
              label: "其他",
            },
          ],
          Payment_method: "0",
          StartTime: "",
          EndTime: "",
          keywork: "",
          tableData: [],
          paymentLoading: true,
          page: 1,
          limit: 10,
          total: 0,
          form: {
            sPay: "0.00",
            rPay: "0.00",
            wPay: "0.00",
          },
          pickerOptions: {
            shortcuts: [
              {
                text: "今天",
                onClick(picker) {
                  const end = new Date();
                  const start = new Date();
                  picker.$emit("pick", [start, end]);
                },
              },
              {
                text: "最近3天",
                onClick(picker) {
                  const end = new Date();
                  const start = new Date();
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
                  picker.$emit("pick", [start, end]);
                },
              },
              {
                text: "最近7天",
                onClick(picker) {
                  const end = new Date();
                  const start = new Date();
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                  picker.$emit("pick", [start, end]);
                },
              },
              {
                text: "最近15天",
                onClick(picker) {
                  const end = new Date();
                  const start = new Date();
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
                  picker.$emit("pick", [start, end]);
                },
              },
              {
                text: "最近30天",
                onClick(picker) {
                  const end = new Date();
                  const start = new Date();
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                  picker.$emit("pick", [start, end]);
                },
              },
              {
                text: "最近90天",
                onClick(picker) {
                  const end = new Date();
                  const start = new Date();
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                  picker.$emit("pick", [start, end]);
                },
              },
              {
                text: "本月",
                onClick(picker) {
                  const end = new Date();
                  const start = new Date();
                  const day = start.getDate() - 1;
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * day);
                  picker.$emit("pick", [start, end]);
                },
              },
            ],
          },
          timeArr: "",
        };
      },
      methods: {
        getPayTypeNameColor(typeName) {
          if (typeName.includes("微信")) {
            return "o-tag-emerald";
          }
          switch (typeName) {
            case "现金":
              return "o-tag-orange";
            case "拉卡拉":
              return "o-tag-blue";
            case "会员余额":
              return "o-tag-fuchsia";
            case "POS机":
              return "o-tag-lime";
            default:
              return "o-tag-gray";
          }
        },
        getSourceColor(source_type) {
          switch (source_type) {
            case "1":
              return "o-tag-gray";
            case "2":
              return "o-tag-pink";
            case "3":
              return "o-tag-emerald";
            default:
              return "o-tag-gray";
          }
        },
        See(id) {
          // window.open("#/Order/seorder/id=" + id);
          location.hash = "/Order/seorder/id=" + id;
        },
        importOrder() {
          var startTime = this.StartTime;
          var endTime = this.EndTime;
          var order_type = this.order_type;
          if (startTime > endTime) {
            return this.$message({
              type: "info",
              message: "开始时间必须小于结束时间",
            });
          }
          var data = {
            startTime: startTime,
            endTime: endTime,
            order_type: order_type,
            payment_method: this.Payment_method,
          };
          const preUrl = "{:url('importOrderList')}";
          const url = this.getUrl(preUrl, data);
          this.$message.warning(
            "数据较多时，将花费较多时间，请耐心等待，建议按时间段帅选条件分多次导出"
          );
          location.href = url;
        },
        getParam(data) {
          let url = "";
          for (var k in data) {
            let value = data[k] !== undefined ? data[k] : "";
            url += `&${k}=${encodeURIComponent(value)}`;
          }
          return url ? url.substring(1) : "";
        },
        /**
         * 将url和参数拼接成完整地址
         * @param {string} url url地址
         * @param {Json} data json对象
         * @returns {string}
         */
        getUrl(url, data) {
          //看原始url地址中开头是否带?，然后拼接处理好的参数
          return (url +=
            (url.indexOf("?") < 0 ? "?" : "&") + this.getParam(data));
        },
        getPaymentList: function () {
          var that = this;
          var startTime = that.StartTime;
          var endTime = that.EndTime;
          var order_type = that.order_type;
          if (startTime > endTime) {
            return that.$message({
              type: "info",
              message: "开始时间必须小于结束时间",
            });
          }
          that.paymentLoading = true;
          $.ajax({
            url: "{:url('getPaymentList')}",
            type: "post",
            data: {
              keywork: that.keywork,
              startTime: startTime,
              endTime: endTime,
              order_type: order_type,
              page: that.page,
              limit: that.limit,
              payment_method: that.Payment_method,
            },
            success: function (res) {
              that.tableData = res.data;
              that.total = res.count;
              that.paymentLoading = false;
              if (that.page == 1) {
                that.form = JSON.parse(res.msg);
              }
            },
          });
        },
        size_change(val) {
          this.limit = val;
          this.page = 1;
          this.getPaymentList();
        },
        /* 切换页数 */
        current_change(val) {
          this.page = val;
          this.getPaymentList();
        },
        getCustomizeCashierData() {
          var vm = this;
          $.ajax({
            url: "{:url('Customizecashier/getData')}",
            data: {},
            success(res) {
              var data = vm.Payment_methods;
              for (var i = 0; i < res.data.length; i++) {
                data.push({
                  value: res.data[i]["pay_type"],
                  label: "【记账】" + res.data[i]["name"],
                });
              }
              vm.Payment_methods = data;
            },
          });
        },
      },
      created: function () {
        const now = new Date();
        const start = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate(),
          0,
          0,
          0
        );
        const end = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate(),
          23,
          59,
          59
        );
        this.timeArr = [start, end];
        this.getCustomizeCashierData();
      },
      watch: {
        timeArr(n) {
          if (n.length == 2) {
            this.StartTime = n[0].getTime();
            this.EndTime = n[1].getTime();
            this.current_change(1);
          }
        },
      },
    });
  });
</script>
